name: ComChemKit CI/CD

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  workflow_dispatch:

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y g++ make cmake clang-tidy

      - name: Verify source files
        run: |
          echo "Checking for required source files..."
          required_files=(
            "src/gaussian/gaussian_high_level_energy.cpp"
            "src/gaussian/gaussian_high_level_energy.h"
            "src/gaussian/gaussian_program.cpp"
            "src/gaussian/gaussian_program.h"
            "src/gaussian/gaussian_extractor.cpp"
            "src/gaussian/gaussian_extractor.h"
            "src/gaussian/gaussian_job_checker.cpp"
            "src/gaussian/gaussian_job_checker.h"
            "src/gaussian/gaussian_command_executor.cpp"
            "src/core/cck_command_system.cpp"
            "src/core/cck_command_system.h"
            "src/core/cck_config_manager.cpp"
            "src/core/cck_config_manager.h"
            "src/core/cck_job_scheduler.cpp"
            "src/core/cck_job_scheduler.h"
            "src/core/cck_qm_program.cpp"
            "src/core/cck_qm_program.h"
            "src/main.cpp"
          )

          missing_files=0
          for file in "${required_files[@]}"; do
            if [ ! -f "$file" ]; then
              echo "Error: Missing $file"
              missing_files=1
            fi
          done

          if [ $missing_files -eq 1 ]; then
            exit 1
          fi
          echo "All required source files found."

      - name: Configure CMake
        run: |
          echo "Configuring CMake..."
          mkdir build 
          cmake -S . -B build \
            -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
            -DCMAKE_BUILD_TYPE=Release \
            -DWITH_GAUSSIAN=ON \
            -DENABLE_EXTRA_WARNINGS=ON

      - name: Static Analysis
        run: |
          echo "Running static analysis..."
          clang-tidy -p build src/main.cpp src/gaussian/*.cpp src/core/*.cpp || echo "Static analysis completed with warnings (non-blocking)"

      - name: Build project
        run: |
          echo "Building the project..."
          cmake --build build --config Release --verbose

      - name: Verify build output
        run: |
          if [ ! -f "build/bin/cck" ]; then
            echo "Error: Build output not found: build/bin/cck"
            exit 1
          fi
          echo "Build output verified successfully"
          ls -l build/bin/cck

      - name: Copy test log files
        run: |
          echo "Copying test log files to build/bin/"
          cp tests/data/test-1.log build/bin/
          cp tests/data/test-2.log build/bin/
          ls -l build/bin/*.log

      - name: Run basic execution test
        run: |
          echo "Running basic execution test..."
          cd build/bin
          ./cck
          if [ ! -f "$(basename $(pwd)).results" ]; then
            echo "Error: Results file was not created!"
            exit 1
          fi
          echo "Execution test passed. Results file was created."

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: cck-ubuntu
          path: build/bin/cck
          if-no-files-found: error

      - name: Upload CMake logs on failure
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cmake-logs-ubuntu
          path: |
            build/CMakeFiles/CMakeOutput.log
            build/CMakeFiles/CMakeError.log
