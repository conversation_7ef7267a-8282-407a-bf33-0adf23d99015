/**
 * @file high_level_energy.h
 * @brief High-level energy calculations with thermal corrections for Gaussian calculations
 * <AUTHOR>
 * @date 2025
 * 
 * This header provides a complete system for combining high-level electronic energies
 * with low-level thermal corrections to calculate accurate thermodynamic properties.
 * The system implements a two-level approach where high-level single-point energies
 * are combined with thermal corrections from lower-level optimizations and frequency
 * calculations.
 * 
 * @section Methodology
 * The high-level energy approach follows this computational strategy:
 * 1. High-level directory: Single-point energy calculations at higher theory level
 * 2. Parent directory: Geometry optimization and frequency calculations at lower level
 * 3. Combination: High-level electronic energy + low-level thermal corrections
 * 4. Corrections: Phase corrections for concentration and temperature effects
 * 
 * @section Energy Components
 * - Electronic energies: SCF, CIS, PCM, CLR corrections
 * - Thermal corrections: Zero-point energy, enthalpy, Gibbs free energy
 * - Phase corrections: Concentration and temperature-dependent corrections
 * - Frequency analysis: Vibrational frequencies for thermal properties
 * 
 * @section Output Formats
 * - Gibbs format: Focus on final Gibbs free energies with key corrections
 * - Components format: Detailed breakdown of all energy contributions
 * - Both formats support temperature and concentration variations
 */

#ifndef HIGH_LEVEL_ENERGY_H
#define HIGH_LEVEL_ENERGY_H

#include <string>
#include <vector>
#include <memory>
#include "gaussian_extractor.h"
#include "../core/cck_constants.h"

/**
 * @struct HighLevelEnergyData
 * @brief Complete energy data container for high-level calculations
 * 
 * This structure holds all energy components and calculated thermodynamic
 * properties for a single high-level energy calculation. It combines
 * high-level electronic energies with low-level thermal corrections
 * and provides comprehensive thermodynamic analysis.
 * 
 * @section Data Organization
 * The structure is organized into logical groups:
 * - High-level electronic energies (from current directory)
 * - Low-level thermal data (from parent directory)
 * - Calculated intermediate values
 * - Final thermodynamic quantities
 * - Additional metadata and status information
 * 
 * @section Energy Hierarchy
 * - High-level: Accurate electronic energies (DFT, MP2, CCSD(T), etc.)
 * - Low-level: Geometry optimization and frequency calculations (B3LYP, etc.)
 * - Combined: Best of both levels for accurate thermodynamics
 */
struct HighLevelEnergyData {
    std::string filename;            ///< Original log file name
    
    /**
     * @defgroup HighLevelEnergies High-Level Electronic Energies
     * @brief Electronic energies from high-level single-point calculations
     * @{
     */
    double scf_high = 0.0;           ///< SCF Done energy from high-level calculation
    double scf_td_high = 0.0;        ///< CIS/TD-DFT energy from high-level calculation
    double scf_equi_high = 0.0;      ///< PCM equilibrium corrected energy
    double scf_clr_high = 0.0;       ///< CLR (Conductor-like screening) corrected energy
    /** @} */
    
    /**
     * @defgroup LowLevelThermal Low-Level Thermal Data
     * @brief Thermal corrections from low-level optimization and frequency calculations
     * @{
     */
    double scf_low = 0.0;            ///< Low-level SCF energy for reference
    double scf_td_low = 0.0;         ///< Low-level CIS energy for reference
    double zpe = 0.0;                ///< Zero-point energy correction
    double tc_enthalpy = 0.0;        ///< Thermal correction to enthalpy
    double tc_gibbs = 0.0;           ///< Thermal correction to Gibbs free energy
    double tc_energy = 0.0;          ///< Thermal correction to internal energy
    double entropy_total = 0.0;      ///< Total entropy (translational + rotational + vibrational)
    /** @} */
    
    /**
     * @defgroup CalculatedValues Calculated Intermediate Values
     * @brief Intermediate values calculated during processing
     * @{
     */
    double tc_only = 0.0;            ///< Thermal correction excluding zero-point energy
    double ts_value = 0.0;           ///< Temperature × entropy term
    double final_scf_high = 0.0;     ///< Final high-level electronic energy (best available)
    double final_scf_low = 0.0;      ///< Final low-level electronic energy (for comparison)
    /** @} */
    
    /**
     * @defgroup ThermodynamicQuantities Final Thermodynamic Quantities
     * @brief Final calculated thermodynamic properties
     * @{
     */
    double enthalpy_hartree = 0.0;   ///< Enthalpy: H = E_high + TC_H (Hartree)
    double gibbs_hartree = 0.0;      ///< Gibbs free energy: G = E_high + TC_G (Hartree)
    double gibbs_hartree_corrected = 0.0; ///< Gibbs free energy with phase correction (Hartree)
    double gibbs_kj_mol = 0.0;       ///< Gibbs free energy in kJ/mol
    double gibbs_ev = 0.0;           ///< Gibbs free energy in eV
    /** @} */
    
    /**
     * @defgroup AdditionalData Additional Metadata and Status
     * @brief Additional calculation data and status information
     * @{
     */
    double lowest_frequency = 0.0;   ///< Lowest vibrational frequency (cm⁻¹)
    double temperature = 298.15;     ///< Temperature for calculation (K)
    double phase_correction = 0.0;   ///< Applied phase correction term
    bool has_scrf = false;           ///< Whether calculation includes SCRF (solvent)
    bool phase_corr_applied = false; ///< Whether phase correction was applied
    std::string status = "UNKNOWN";  ///< Job completion status (DONE, ERROR, UNDONE)
    /** @} */
    
    /**
     * @brief Constructor with filename initialization
     * @param fname Name of the log file for this calculation
     */
    HighLevelEnergyData(const std::string& fname) : filename(fname) {}
};

/**
 * @class HighLevelEnergyCalculator
 * @brief Complete high-level energy calculation and analysis system
 * 
 * The HighLevelEnergyCalculator class provides a comprehensive solution for
 * combining high-level electronic energies with low-level thermal corrections
 * to calculate accurate thermodynamic properties. It implements the two-level
 * computational approach commonly used in computational chemistry.
 * 
 * @section Calculation Strategy
 * The calculator follows this methodology:
 * 1. Extract high-level electronic energies from current directory
 * 2. Extract thermal corrections from parent directory calculations
 * 3. Combine energies using appropriate theoretical framework
 * 4. Apply temperature and concentration corrections
 * 5. Output results in user-specified formats
 * 
 * @section Directory Structure
 * Expected directory organization:
 * ```
 * parent_directory/          # Low-level calculations (opt+freq)
 * ├── compound1.log         # Optimization and frequency calculation
 * ├── compound2.log
 * └── high_level_dir/       # High-level calculations (single points)
 *     ├── compound1.log     # High-level single-point energy
 *     └── compound2.log
 * ```
 * 
 * @section Features
 * - Automatic detection of calculation types and methods
 * - Support for various high-level methods (DFT, MP2, CCSD(T))
 * - Temperature and concentration-dependent corrections
 * - Multiple output formats for different analysis needs
 * - Comprehensive error handling and validation
 * - Integration with job scheduler resource management
 */
class HighLevelEnergyCalculator {
public:
    /**
     * @brief Constructor with temperature and concentration specification
     * @param temp Temperature for thermodynamic calculations (K, default: 298.15)
     * @param concentration_m Concentration in mol/L for phase corrections (default: 1.0)
     * 
     * Initializes the calculator with specified thermodynamic conditions.
     * Temperature affects thermal corrections and phase corrections, while
     * concentration affects phase corrections for solution-phase calculations.
     */
    HighLevelEnergyCalculator(double temperature = cck::constants::defaults::TEMPERATURE,
                             double concentration = cck::constants::defaults::CONCENTRATION);
    
    /**
     * @defgroup MainCalculation Main Calculation Functions
     * @brief Primary functions for high-level energy calculations
     * @{
     */
    
    /**
     * @brief Calculate high-level energy for a single file
     * @param high_level_file Path to high-level calculation log file
     * @return HighLevelEnergyData with complete energy analysis
     * 
     * Processes a single high-level calculation file and combines it with
     * corresponding low-level thermal data to produce complete thermodynamic
     * properties. This is the core calculation function.
     * 
     * @section Process
     * 1. Extract high-level electronic energies from specified file
     * 2. Locate and extract thermal data from corresponding parent file
     * 3. Combine energies using appropriate theoretical framework
     * 4. Apply temperature and concentration corrections
     * 5. Calculate all thermodynamic quantities
     */
    HighLevelEnergyData calculate_high_level_energy(const std::string& high_level_file);
    
    /**
     * @brief Process entire directory of high-level calculations
     * @param extension File extension to process (default: ".log")
     * @return Vector of HighLevelEnergyData for all processed files
     * 
     * Processes all files in the current directory with the specified extension,
     * calculating high-level energies for each. Provides batch processing
     * capability for large sets of calculations.
     */
    std::vector<HighLevelEnergyData> process_directory(const std::string& extension = ".log");
    
    /** @} */ // end of MainCalculation group
    
    /**
     * @defgroup OutputFunctions Output Formatting Functions
     * @brief Functions for formatting and displaying calculation results
     * @{
     */
    
    /**
     * @brief Print results in Gibbs free energy focused format
     * @param results Vector of calculation results to display
     * @param quiet Suppress header and summary information (default: false)
     * @param output_file Optional file stream for output (default: nullptr for console)
     * 
     * Outputs results with focus on final Gibbs free energies and key
     * corrections. This format emphasizes the most important thermodynamic
     * quantity for most chemical applications.
     */
    void print_gibbs_format(const std::vector<HighLevelEnergyData>& results, 
                           bool quiet = false, std::ostream* output_file = nullptr);
    
    /**
     * @brief Print results with detailed energy component breakdown
     * @param results Vector of calculation results to display
     * @param quiet Suppress header and summary information (default: false)
     * @param output_file Optional file stream for output (default: nullptr for console)
     * 
     * Outputs comprehensive breakdown of all energy components including
     * high-level and low-level contributions, thermal corrections, and
     * intermediate calculated values. Useful for detailed analysis and debugging.
     */
    void print_components_format(const std::vector<HighLevelEnergyData>& results, 
                                bool quiet = false, std::ostream* output_file = nullptr);
    
    /** @} */ // end of OutputFunctions group
    
    /**
     * @defgroup Configuration Configuration Management
     * @brief Functions for managing calculator parameters
     * @{
     */
    
    /**
     * @brief Set temperature for thermodynamic calculations
     * @param temp Temperature in Kelvin
     * 
     * Updates the temperature used for thermal corrections and phase
     * corrections. Affects all subsequent calculations.
     */
    void set_temperature(double temp) { temperature_ = temp; }
    
    /**
     * @brief Set concentration for phase corrections
     * @param conc_m Concentration in mol/L (molarity)
     * 
     * Updates the concentration used for phase corrections in solution-phase
     * calculations. Automatically converts to mol/m³ for internal calculations.
     */
    void set_concentration(double conc_m) { 
        concentration_m_ = conc_m; 
        concentration_mol_m3_ = conc_m * 1000.0;
    }
    
    /**
     * @brief Get current temperature setting
     * @return Temperature in Kelvin
     */
    double get_temperature() const { return temperature_; }
    
    /**
     * @brief Get current concentration setting
     * @return Concentration in mol/L
     */
    double get_concentration_m() const { return concentration_m_; }
    
    /** @} */ // end of Configuration group
    
private:
    double temperature_;           ///< Temperature for calculations (K)
    double concentration_m_;       ///< Concentration in mol/L
    double concentration_mol_m3_;  ///< Concentration in mol/m³ (for calculations)
    
    /**
     * @defgroup EnergyExtraction Energy Extraction Helper Functions
     * @brief Private functions for extracting energy values from log files
     * @{
     */
    
    /**
     * @brief Extract SCF energy from high-level calculation file
     * @param filename Path to high-level log file
     * @return SCF energy in Hartree
     */
    double extract_high_level_scf(const std::string& filename);
    
    /**
     * @brief Extract specific energy value from log file using pattern matching
     * @param filename Path to log file
     * @param pattern Search pattern to locate energy line
     * @param field_index Field index in matched line (0-based)
     * @param occurrence Which occurrence to use (-1 for last)
     * @return Extracted energy value
     */
    double extract_value_from_file(const std::string& filename, const std::string& pattern, 
                                  int field_index, int occurrence = -1);
    
    /**
     * @brief Extract thermal correction data from low-level calculation
     * @param parent_file Path to parent directory log file
     * @param data Reference to HighLevelEnergyData to populate
     * @return true if thermal data successfully extracted, false otherwise
     */
    bool extract_low_level_thermal_data(const std::string& parent_file, HighLevelEnergyData& data);
    
    /** @} */ // end of EnergyExtraction group
    
    /**
     * @defgroup CalculationHelpers Calculation Helper Functions
     * @brief Private functions for thermodynamic calculations and corrections
     * @{
     */
    
    /**
     * @brief Calculate phase correction for concentration effects
     * @param temp Temperature in Kelvin
     * @param concentration_mol_m3 Concentration in mol/m³
     * @return Phase correction in Hartree
     */
    double calculate_phase_correction(double temp, double concentration_mol_m3);
    
    /**
     * @brief Calculate thermal correction excluding zero-point energy
     * @param tc_with_zpe Total thermal correction including ZPE
     * @param zpe Zero-point energy correction
     * @return Thermal correction without ZPE
     */
    double calculate_thermal_only(double tc_with_zpe, double zpe);
    
    /**
     * @brief Calculate temperature × entropy term
     * @param entropy_total Total entropy
     * @param temp Temperature in Kelvin
     * @return T×S value in Hartree
     */
    double calculate_ts_value(double entropy_total, double temp);
    
    /**
     * @brief Extract lowest vibrational frequency from parent file
     * @param parent_file Path to frequency calculation log file
     * @return Lowest frequency in cm⁻¹
     */
    double extract_lowest_frequency(const std::string& parent_file);
    
    /**
     * @brief Determine job completion status from log file
     * @param filename Path to log file to analyze
     * @return Status string ("DONE", "ERROR", "UNDONE")
     */
    std::string determine_job_status(const std::string& filename);
    
    /** @} */ // end of CalculationHelpers group
    
    /**
     * @defgroup FileUtilities File System Utility Functions
     * @brief Private functions for file operations and path management
     * @{
     */
    
    /**
     * @brief Get corresponding parent directory file path
     * @param high_level_file Path to high-level calculation file
     * @return Path to corresponding parent directory file
     */
    std::string get_parent_file(const std::string& high_level_file);
    
    /**
     * @brief Check if file exists and is accessible
     * @param filename Path to file to check
     * @return true if file exists, false otherwise
     */
    bool file_exists(const std::string& filename);
    
    /**
     * @brief Read complete file content
     * @param filename Path to file to read
     * @return Complete file content as string
     */
    std::string read_file_content(const std::string& filename);
    
    /**
     * @brief Read last N lines from file
     * @param filename Path to file to read
     * @param lines Number of lines to read from end (default: 10)
     * @return String containing last N lines
     */
    std::string read_file_tail(const std::string& filename, int lines = 10);
    
    /** @} */ // end of FileUtilities group
    
    /**
     * @defgroup OutputFormatting Output Formatting Helper Functions
     * @brief Private functions for formatting output display
     * @{
     */
    
    /**
     * @brief Print header for Gibbs free energy format output
     * @param output_file Optional file stream for output (default: nullptr for console)
     */
    void print_gibbs_header(std::ostream* output_file = nullptr);
    
    /**
     * @brief Print header for energy components format output
     * @param output_file Optional file stream for output (default: nullptr for console)
     */
    void print_components_header(std::ostream* output_file = nullptr);
    
    /**
     * @brief Format filename for display with length limit
     * @param filename Original filename
     * @param max_length Maximum display length (default: 53)
     * @return Formatted filename string
     */
    std::string format_filename(const std::string& filename, int max_length = 53);
    
    /**
     * @brief Print summary information about calculation
     * @param last_parent_file Path to parent file used for thermal data
     * @param output_file Optional file stream for output (default: nullptr for console)
     */
    void print_summary_info(const std::string& last_parent_file, std::ostream* output_file = nullptr);
    
    /** @} */ // end of OutputFormatting group

public:
    /**
     * @defgroup DynamicOutputFormatting Dynamic Output Formatting
     * @brief Advanced formatting functions with dynamic column sizing
     * @{
     */
    
    /**
     * @brief Print results in Gibbs format with dynamic column widths
     * @param results Vector of calculation results to display
     * @param quiet Suppress header and summary information (default: false)
     * @param output_file Optional file stream for output (default: nullptr for console)
     * 
     * Uses dynamic column sizing based on actual data content for better readability.
     * This is the recommended formatting method for most use cases.
     */
    void print_gibbs_format_dynamic(const std::vector<HighLevelEnergyData>& results, 
                                   bool quiet = false, std::ostream* output_file = nullptr);
    
    /**
     * @brief Print results in components format with dynamic column widths
     * @param results Vector of calculation results to display
     * @param quiet Suppress header and summary information (default: false)
     * @param output_file Optional file stream for output (default: nullptr for console)
     * 
     * Uses dynamic column sizing based on actual data content for better readability.
     * This is the recommended formatting method for most use cases.
     */
    void print_components_format_dynamic(const std::vector<HighLevelEnergyData>& results, 
                                        bool quiet = false, std::ostream* output_file = nullptr);
    
    /**
     * @brief Print results in Gibbs format with static column widths
     * @param results Vector of calculation results to display
     * @param quiet Suppress header and summary information (default: false)
     * @param output_file Optional file stream for output (default: nullptr for console)
     * 
     * Uses fixed column widths for consistent formatting. Primarily used for
     * backwards compatibility or when consistent column alignment is required.
     */
    void print_gibbs_format_static(const std::vector<HighLevelEnergyData>& results, 
                                  bool quiet = false, std::ostream* output_file = nullptr);
    
    /**
     * @brief Print results in components format with static column widths
     * @param results Vector of calculation results to display
     * @param quiet Suppress header and summary information (default: false)
     * @param output_file Optional file stream for output (default: nullptr for console)
     * 
     * Uses fixed column widths for consistent formatting. Primarily used for
     * backwards compatibility or when consistent column alignment is required.
     */
    void print_components_format_static(const std::vector<HighLevelEnergyData>& results, 
                                       bool quiet = false, std::ostream* output_file = nullptr);

    /** @} */ // end of DynamicOutputFormatting group

private:
    /**
     * @defgroup DynamicFormatting Dynamic Column Formatting
     * @brief Helper functions for calculating optimal column widths
     * @{
     */
    
    /**
     * @brief Calculate optimal column widths for Gibbs format output
     * @param results Vector of calculation results
     * @return Vector of column widths for each data column
     */
    std::vector<int> calculate_gibbs_column_widths(const std::vector<HighLevelEnergyData>& results);
    
    /**
     * @brief Calculate optimal column widths for components format output
     * @param results Vector of calculation results
     * @return Vector of column widths for each data column
     */
    std::vector<int> calculate_components_column_widths(const std::vector<HighLevelEnergyData>& results);
    
    /**
     * @brief Print header for Gibbs format with dynamic column widths
     * @param column_widths Vector of column widths
     * @param output_file Optional file stream for output
     */
    void print_gibbs_header_dynamic(const std::vector<int>& column_widths, std::ostream* output_file = nullptr);
    
    /**
     * @brief Print header for components format with dynamic column widths
     * @param column_widths Vector of column widths
     * @param output_file Optional file stream for output
     */
    void print_components_header_dynamic(const std::vector<int>& column_widths, std::ostream* output_file = nullptr);
    
    /** @} */ // end of DynamicFormatting group
};

/**
 * @namespace HighLevelEnergyUtils
 * @brief Utility functions supporting high-level energy calculations
 * 
 * The HighLevelEnergyUtils namespace provides a comprehensive collection
 * of utility functions that support high-level energy calculations including
 * file operations, energy parsing, unit conversions, and data validation.
 */
namespace HighLevelEnergyUtils {
    /**
     * @defgroup FileDirectoryUtils File and Directory Utilities
     * @brief Utilities for file system operations in high-level calculations
     * @{
     */
    
    /**
     * @brief Find all log files in specified directory
     * @param directory Target directory to search (default: current directory)
     * @return Vector of log file paths found in directory
     * 
     * Searches for Gaussian log files suitable for high-level energy processing.
     * Filters files by extension and validates accessibility.
     */
    std::vector<std::string> find_log_files(const std::string& directory = ".");
    
    /**
     * @brief Get name of current working directory
     * @return Current directory name (not full path)
     * 
     * Extracts just the directory name for use in output formatting
     * and directory structure validation.
     */
    std::string get_current_directory_name();
    
    /**
     * @brief Validate that current directory is suitable for high-level calculations
     * @return true if directory structure is valid, false otherwise
     * 
     * Checks that the current directory contains high-level calculation files
     * and that a corresponding parent directory with thermal data exists.
     */
    bool is_valid_high_level_directory();
    
    /** @} */ // end of FileDirectoryUtils group
    
    /**
     * @defgroup EnergyParsingUtils Energy Parsing Utilities
     * @brief Utilities for parsing energy values from Gaussian log files
     * @{
     */
    
    /**
     * @brief Parse energy value from a text line
     * @param line Text line containing energy information
     * @param field_index Index of field containing energy (0-based)
     * @return Parsed energy value
     * 
     * Safely parses energy values from formatted text lines with
     * error handling for malformed input.
     */
    double parse_energy_value(const std::string& line, int field_index);
    
    /**
     * @brief Extract all vibrational frequencies from log file content
     * @param content Complete log file content
     * @return Vector of frequencies in cm⁻¹
     * 
     * Parses frequency calculation output to extract all vibrational
     * frequencies for thermal property calculations.
     */
    std::vector<double> extract_frequencies(const std::string& content);
    
    /**
     * @brief Find lowest frequency from frequency list
     * @param frequencies Vector of frequencies to analyze
     * @return Lowest frequency value
     * 
     * Identifies the lowest vibrational frequency, which is important
     * for identifying transition states and unstable structures.
     */
    double find_lowest_frequency(const std::vector<double>& frequencies);
    
    /** @} */ // end of EnergyParsingUtils group
    
    /**
     * @defgroup ConversionUtils Unit Conversion Utilities
     * @brief Utilities for converting between different energy units
     * @{
     */
    
    /**
     * @brief Convert energy from Hartree to kJ/mol
     * @param hartree Energy value in Hartree (atomic units)
     * @return Energy value in kJ/mol
     */
    double hartree_to_kj_mol(double hartree);
    
    /**
     * @brief Convert energy from Hartree to eV
     * @param hartree Energy value in Hartree (atomic units)
     * @return Energy value in electron volts
     */
    double hartree_to_ev(double hartree);
    
    /** @} */ // end of ConversionUtils group
    
    /**
     * @defgroup ValidationUtils Validation Utilities
     * @brief Utilities for validating calculation parameters and results
     * @{
     */
    
    /**
     * @brief Validate temperature value for physical reasonableness
     * @param temp Temperature value in Kelvin
     * @return true if temperature is physically reasonable, false otherwise
     * 
     * Checks that temperature is within reasonable bounds for chemical
     * calculations (typically 0-2000 K range).
     */
    bool validate_temperature(double temp);
    
    /**
     * @brief Validate concentration value for chemical reasonableness
     * @param conc Concentration value in mol/L
     * @return true if concentration is chemically reasonable, false otherwise
     * 
     * Checks that concentration is within reasonable bounds for solution
     * chemistry calculations (typically 10⁻⁶ to 10² M range).
     */
    bool validate_concentration(double conc);
    
    /**
     * @brief Validate complete energy data structure for consistency
     * @param data HighLevelEnergyData structure to validate
     * @return true if energy data is consistent and reasonable, false otherwise
     * 
     * Performs comprehensive validation of energy data including:
     * - Energy value reasonableness
     * - Internal consistency between different energy components
     * - Proper completion status
     * - Frequency data validity
     */
    bool validate_energy_data(const HighLevelEnergyData& data);
    
    /** @} */ // end of ValidationUtils group
}

#endif // HIGH_LEVEL_ENERGY_H