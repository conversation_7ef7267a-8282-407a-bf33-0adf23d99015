# ComChemKit (CCK) Configuration File
# Save this as .cck.conf in your home directory or current working directory
#
# Lines starting with # or ; are comments
# Values can be quoted with " or ' if they contain spaces
# This configuration file controls the behavior of all quantum chemistry modules
#

# ==================================================
# Core Settings (Universal)
# ==================================================

# Default quantum chemistry program to use
# This is the most important setting - determines which program module is used
# Options: gaussian (more programs like orca, qchem, molpro coming soon)
# When not specified or file not available, defaults to 'gaussian'
default_program = gaussian

# Program modules to enable (comma-separated)
# Currently only 'gaussian' is implemented
enabled_modules = gaussian

# Default decimal precision for output
decimal_precision = 6

# Scientific notation for small numbers
use_scientific_notation = false

# Default time zone for timestamps (empty = system default)
timezone =

# Enable quiet mode by default
quiet_mode = false

# Automatically backup files before moving
auto_backup = false

# ==================================================
# Physical Constants and Units
# ==================================================

# Temperature for calculations (Kelvin)
temperature = 298.15

# Standard concentration (mol/L)
concentration = 1.0

# Standard pressure (atm)
pressure = 1.0

# Phase correction (kcal/mol, 1atm→1M)
phase_correction = 1.89

# Energy threshold for zero (Hartree)
zero_threshold = 1e-10

# Frequency threshold (cm⁻¹)
freq_threshold = -50.0

# ==================================================
# File and Directory Management
# ==================================================

# Base directory for organized files
base_directory = .

# Directory structure
directories {
    done = done
    errors = errorJobs
    pcm_errors = PCMMkU
    backups = backup
}

# File handling
files {
    # Maximum file size to process (MB)
    max_size = 100

    # File extensions to process
    extensions = .log,.out,.com,.gjf,.gau

    # Create backups before modifications
    create_backups = true

    # Move associated files together
    move_related = true
}

# ==================================================
# Performance and Resource Management
# ==================================================

# Thread management
threads {
    # Thread count (number/half/max)
    count = half

    # Maximum threads allowed
    max_threads = 32
}

# Memory management
memory {
    # Memory limit in MB (0 = auto)
    limit = 0

    # Minimum required memory (MB)
    min_required = 512
}

# Cluster settings
cluster {
    # Safety mode (auto/strict/relaxed)
    safety = auto

    # Resource detection (auto/manual)
    detection = auto
}

# I/O settings
io {
    # Maximum open files
    max_handles = 20

    # Buffer size (KB)
    buffer_size = 64
}

# ==================================================
# Output Formatting and Display
# ==================================================

# Output format
format {
    # Output style (text/csv/json)
    style = text

    # Field separator for text output
    separator = "\t"

    # Column widths [name,energy,freq,...]
    column_widths = 20,12,10,12,12,12,10,8,5,5

    # Include metadata in output
    include_metadata = true

    # Include timestamps
    include_timestamps = true

    # Date format
    date_format = "%Y-%m-%d %H:%M:%S"
}

# File naming
output {
    # Results file template
    results_template = {dirname}.results

    # CSV file template
    csv_template = {dirname}.csv

    # Backup file suffix
    backup_suffix = .bak
}

# ==================================================
# Configuration File Usage Notes
# ==================================================
#
# Configuration files are searched in this order:
# 1. ./.cck.conf (current working directory)
# 2. ~/.cck.conf (user home directory)
# 3. If no file found, built-in defaults are used
#
# The 'default_program' setting determines which quantum chemistry
# program module is used for all commands. Currently supported:
# - gaussian: For Gaussian 09/16 log files (default)
#
# Future versions will support additional programs like ORCA, Q-Chem, etc.
#
# End of CCK configuration file
