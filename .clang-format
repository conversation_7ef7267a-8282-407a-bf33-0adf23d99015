# Clang-Format Configuration for Gaussian Extractor
# Modern C++ style guide based on LLVM style with modifications

# Basic Settings
Language: Cpp
Standard: c++20
BasedOnStyle: LLVM

# Indentation
IndentWidth: 4
TabWidth: 4
UseTab: Never
AccessModifierOffset: -4
IndentCaseLabels: true
IndentPPDirectives: BeforeHash
NamespaceIndentation: All

# Line Breaking
ColumnLimit: 120
AlwaysBreakTemplateDeclarations: Yes
BreakBeforeBraces: Custom
BraceWrapping:
  AfterClass: true
  AfterControlStatement: true
  AfterEnum: true
  AfterFunction: true
  AfterNamespace: true
  AfterStruct: true
  AfterUnion: true
  BeforeCatch: true
  BeforeElse: true
  IndentBraces: false
  SplitEmptyFunction: false
  SplitEmptyRecord: false
  SplitEmptyNamespace: false

# Alignment and Spacing
AlignAfterOpenBracket: Align
AlignConsecutiveAssignments: true
AlignConsecutiveDeclarations: true
AlignEscapedNewlines: Right
AlignOperands: true
AlignTrailingComments: true
PointerAlignment: Left
ReferenceAlignment: Left
MaxEmptyLinesToKeep: 2

# Function Declaration Style
AllowAllParametersOfDeclarationOnNextLine: false
BinPackArguments: false
BinPackParameters: false
ExperimentalAutoDetectBinPacking: false

# Class Layout
FixNamespaceComments: true
SortIncludes: true
SortUsingDeclarations: true

# Template Style
SpaceAfterTemplateKeyword: true
SpaceBeforeParens: ControlStatements

# Other Spacing
SpaceAfterCStyleCast: false
SpaceBeforeAssignmentOperators: true
SpaceBeforeCpp11BracedList: false
SpaceInEmptyParentheses: false
SpacesBeforeTrailingComments: 2
SpacesInAngles: false
SpacesInCStyleCastParentheses: false
SpacesInContainerLiterals: false
SpacesInParentheses: false
SpacesInSquareBrackets: false

# Comments
ReflowComments: true
CommentPragmas: '^ IWYU pragma:'

# Modern C++ Features
AllowShortFunctionsOnASingleLine: Empty
AllowShortIfStatementsOnASingleLine: Never
AllowShortLoopsOnASingleLine: false
AllowShortLambdasOnASingleLine: Empty
CompactNamespaces: false
Cpp11BracedListStyle: true

# Include Categories
IncludeCategories:
  - Regex:           '^"'
    Priority:        2
  - Regex:           '^<.*\.h>'
    Priority:        3
  - Regex:           '^<.*'
    Priority:        4
